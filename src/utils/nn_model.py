#%% ---- Import Required Libraries ----
import json
import os
from datetime import datetime

import numpy as np
import tensorflow as tf

#%% ---- Random Seed ----
seed=42
tf.random.set_seed(seed)

# %% Set the default floating-point precision for Keras backend operations.
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

#%% ---- Model class ----
class NN_Model (tf.keras.Model):

    def __init__ (self, norm_params_, hidden_dims, activation_fn):
        """
        Configure the layers of the model based on the given number of hidden layers (excl. output layer) and nodes.

        Parameters:
        - hidden_dims (list): List of integers representing the number of nodes in each hidden layer.
        - activation_func (str): Activation function for the hidden layers.
        """
        super().__init__()
        self.num_inputs = 2
        self.num_outputs = 1
        self.activation_fn = activation_fn
        self.norm_params = norm_params_

        self.hidden_layers = []
        for i, h_dim in enumerate(hidden_dims):
            self.hidden_layers.append(tf.keras.layers.Dense(
                h_dim,
                activation=activation_fn,
                name=f'hidden_layer_{i+1}',
                dtype=GLOBAL_DTYPE
            ))

        self.out_layer = tf.keras.layers.Dense(self.num_outputs, name='out_layer', dtype=GLOBAL_DTYPE)

        self.build(input_shape=(None, self.num_inputs))

    def call (self, inputs):
        inp_eps_pl_t = tf.slice(inputs, [0, 0], [-1, 1])
        inp_s_t = tf.slice(inputs, [0, 1], [-1, 1])
        n_concat = tf.concat([inp_eps_pl_t, inp_s_t], axis=1, name='concat_inputs')
        for layer in self.hidden_layers:
            n_concat = layer(n_concat)
        n_psi_max_t = self.out_layer(n_concat)

        return n_psi_max_t