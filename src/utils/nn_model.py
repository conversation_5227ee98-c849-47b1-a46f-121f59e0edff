#%% ---- Import Required Libraries ----
import json
import os
from datetime import datetime

import numpy as np
import tensorflow as tf

#%% ---- Random Seed ----
seed=42
tf.random.set_seed(seed)

# %% Set the default floating-point precision for Keras backend operations.
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

#%% ---- Model class ----
class NN_Model (tf.keras.Model):

    def __init__ (self, norm_params_, hidden_dims, activation_fn):
        """
        Configure the layers of the model based on the given number of hidden layers (excl. output layer) and nodes.

        Parameters:
        - hidden_dims (list): List of integers representing the number of nodes in each hidden layer.
        - activation_func (str): Activation function for the hidden layers.
        """
        super().__init__()
        self.num_inputs = 2
        self.num_outputs = 1
        self.activation_fn = activation_fn
        self.norm_params = norm_params_

        self.hidden_layers = []
        for i, h_dim in enumerate(hidden_dims):
            self.hidden_layers.append(tf.keras.layers.Dense(
                h_dim,
                activation=activation_fn,
                name=f'hidden_layer_{i+1}',
                dtype=GLOBAL_DTYPE
            ))

        self.out_layer = tf.keras.layers.Dense(self.num_outputs, name='out_layer', dtype=GLOBAL_DTYPE)

        self.build(input_shape=(None, self.num_inputs))

    def call (self, inputs):
        """
        Process tensor inputs through the network.

        Parameters:
        - inputs: Tensor of shape (batch_size, 12) where:
                  - columns 0-5: plastic strain tensor components (eps_pl_11, eps_pl_22, eps_pl_33, eps_pl_12, eps_pl_23, eps_pl_13)
                  - columns 6-11: stress tensor components (s_11, s_22, s_33, s_12, s_23, s_13)

        Returns:
        - n_psi_max_t: Predicted maximum free energy
        """
        # Extract plastic strain tensor (first 6 components)
        inp_eps_pl_tensor = tf.slice(inputs, [0, 0], [-1, self.tensor_dim], name='plastic_strain_tensor')

        # Extract stress tensor (next 6 components)
        inp_stress_tensor = tf.slice(inputs, [0, self.tensor_dim], [-1, self.tensor_dim], name='stress_tensor')

        # Concatenate the two tensors for processing
        # This maintains the conceptual structure of 2 tensor inputs while flattening for the dense layers
        n_concat = tf.concat([inp_eps_pl_tensor, inp_stress_tensor], axis=1, name='concat_tensor_inputs')

        # Process through hidden layers
        for layer in self.hidden_layers:
            n_concat = layer(n_concat)

        # Output layer
        n_psi_max_t = self.out_layer(n_concat)

        return n_psi_max_t