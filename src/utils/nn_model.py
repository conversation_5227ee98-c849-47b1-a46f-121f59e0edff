#%% ---- Import Required Libraries ----
import json
import os
from datetime import datetime

import numpy as np
import tensorflow as tf

#%% ---- Random Seed ----
seed=42
tf.random.set_seed(seed)

# %% Set the default floating-point precision for Keras backend operations.
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

#%% ---- Model class ----
class NN_Model (tf.keras.Model):

    def __init__ (self, norm_params, hidden_dims, activation_fn):
        """
        Configure the layers of the model based on the given number of hidden layers (excl. output layer) and nodes.

        Parameters:
        - norm_params_ (list): List of tuples containing the normalization parameters (α, β) for each input feature.
        - hidden_dims (list): List of integers representing the number of nodes in each hidden layer.
        - activation_func (str): Activation function for the hidden layers.
        """
        super().__init__()
        self.num_inputs = 12
        self.num_outputs = 1
        self.activation_fn = activation_fn
        self.norm_params = norm_params

        self.hidden_layers = []
        for i, h_dim in enumerate(hidden_dims):
            self.hidden_layers.append(tf.keras.layers.Dense(
                h_dim,
                activation=activation_fn,
                name=f'hidden_layer_{i+1}',
                dtype=GLOBAL_DTYPE
            ))

        self.out_layer = tf.keras.layers.Dense(self.num_outputs, name='out_layer', dtype=GLOBAL_DTYPE)

        self.build(input_shape=(None, self.num_inputs))

    def call (self, inputs):
        """
        Process tensor inputs through the network.

        Parameters:
        - inputs: Tensor of shape (batch_size, 12) where:
                  - columns 0-5: plastic strain tensor components (eps_pl_11, eps_pl_22, eps_pl_33, eps_pl_12, eps_pl_23, eps_pl_13)
                  - columns 6-11: stress tensor components (s_11, s_22, s_33, s_12, s_23, s_13)

        Returns:
        - n_psi_max_t: Predicted normalized maximum free energy
        """
        #! NOTE: Separation of the inputs into 2 tensors is just for conceptual understanding, and possibly future extensions.
        self.tensor_dim = 6
        # plastic strain tensor (first 6 components)
        inp_eps_pl_tensor = tf.slice(inputs, [0, 0], [-1, self.tensor_dim], name='plastic_strain_tensor')
        # stress tensor (next 6 components)
        inp_stress_tensor = tf.slice(inputs, [0, self.tensor_dim], [-1, self.tensor_dim], name='stress_tensor')

        n_concat = tf.concat([inp_eps_pl_tensor, inp_stress_tensor], axis=1, name='concat_tensor_inputs')

        for layer in self.hidden_layers:
            n_concat = layer(n_concat)
        n_psi_max_t = self.out_layer(n_concat)

        return n_psi_max_t

    @classmethod
    def train_model (cls, model_instance, train_data, val_data, LearningRate, nEpochs, bSize, silent_training=False, early_stopping=None, lr_schedule_type='constant'):
        """
        Train the model with learning rate scheduling.

        Parameters:
        - model_instance: Instance of NNf_TwoInputs to train
        - train_data: Training data with shape (num_samples, num_features)
        - val_data: Validation data with shape (num_samples, num_features)
        - LearningRate: Initial learning rate for the optimizer
        - nEpochs: Number of epochs to train
        - bSize: Batch size
        - silent_training: If True, suppress training output
        - early_stopping: Early stopping callback
        - lr_schedule_type: Type of learning rate schedule to use ('exponential', 'cosine', or 'constant')

        Returns:
        - history: Training history
        """

        silent_verbose = 0 if silent_training else 1
        if early_stopping is not None:
            early_stopping = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=500, restore_best_weights=True)

        callbacks = [early_stopping]

        if lr_schedule_type == 'exponential':
            decay_steps = nEpochs // 2  # Decay significantly over 1/2 of training
            decay_rate = 0.75   # Decay the learning rate every decay_steps
            lr_scheduler = tf.keras.optimizers.schedules.ExponentialDecay(
                initial_learning_rate=LearningRate,
                decay_steps=decay_steps,
                decay_rate=decay_rate,
                staircase=False  # Smooth decay
            )
            print(f"Using exponential learning rate decay: initial={LearningRate}, decay_steps={decay_steps}, decay_rate={decay_rate}")

        if lr_schedule_type == 'cosine':
            first_decay_steps = nEpochs // 5
            lr_scheduler = tf.keras.optimizers.schedules.CosineDecayRestarts(
                initial_learning_rate=LearningRate,
                first_decay_steps=first_decay_steps,
                t_mul=1.5,          # Each cycle gets longer
                m_mul=0.75,         # Each restart has lower max learning rate
                alpha=1e-5          # Minimum learning rate
            )
            print(f"Using cosine learning rate decay: initial={LearningRate}, first_decay_steps={first_decay_steps}")

        if lr_schedule_type == 'constant':
            lr_scheduler = LearningRate
            print(f"Using constant learning rate: {LearningRate}")

        # Learning rate scheduler callback to log the learning rate
        class LRLogger (tf.keras.callbacks.Callback):
            def on_epoch_end (self, epoch, logs=None):
                if not silent_training and epoch % 100 == 0:
                    lr = self.model.optimizer.learning_rate
                    if hasattr(lr, '__call__'):
                        print(f"\nEpoch {epoch}: Learning rate: {lr(epoch).numpy():.10f}")
                    else: print(f"\nEpoch {epoch}: Learning rate: {float(lr):.10f}")
        if not silent_training:
            callbacks.append(LRLogger())

        optimizer = tf.keras.optimizers.Nadam(learning_rate=lr_scheduler)
        model_instance.compile(optimizer=optimizer, loss=['mae'], metrics=['mape'])
        history = model_instance.fit(
            x=train_data[:, 0:12],
            y=train_data[:, -1],
            validation_data=(val_data[:, 0:12], val_data[:, -1]),
            epochs=nEpochs,
            batch_size=bSize,
            callbacks=callbacks,
            verbose=silent_verbose
        )

        if not silent_verbose:
            print("\n...Training completed in", len(history.history['loss']), "epochs.")

        return history