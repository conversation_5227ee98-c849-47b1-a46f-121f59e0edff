"""
This contains utility functions for the project.

Date: 2025-07-07
Author: <PERSON><PERSON><PERSON>
"""

#%% ---- Importing ----
import os
from pathlib import Path

import numpy as np

#%% ---- Set Global Dtype ----
GLOBAL_DTYPE = np.float32

#%% ---- Functions ----
def get_raw_data(folder_name, E, file_names:dict=None):
    '''Read the data from the folder and return the raw data as a numpy array consisting
    strain(e11), stress(s11), state variable(sv1) and free energy.

    Parameters:
    - folder_name (str): The name of the folder containing the data.
    - E (float): Young's modulus.
    - file_names (dict): Dictionary containing the names of the data files.
        Default is None, which uses the default file names.

    Returns:
    - data (numpy.ndarray): Numpy array containing the raw data.
    '''

    if file_names is None:
        file_names = {
            'strain': 'strain.txt',
            'stress': 'stress.txt',
            'state_var': 'state_var.txt'
        }

    base_path = Path(__file__).parent
    data_path = base_path / '../../data' / folder_name

    if not data_path.exists():
        raise FileNotFoundError(f"Data folder '{data_path}' does not exist.")
    if not data_path.is_dir():
        raise NotADirectoryError(f"Path '{data_path}' is not a directory.")

    files = {
        name: data_path / file_name
        for name, file_name in file_names.items()
    }

    for name, file_path in files.items():
        if not file_path.exists():
            raise FileNotFoundError(f"File '{file_path}' does not exist.")
        if not file_path.is_file():
            raise IsADirectoryError(f"Path '{file_path}' is not a file.")

    try:
        strain = np.loadtxt(files['strain'], dtype=GLOBAL_DTYPE)
        stress = np.loadtxt(files['stress'], dtype=GLOBAL_DTYPE)
        state = np.loadtxt(files['state_var'], dtype=GLOBAL_DTYPE)
    except ValueError as e:
        raise ValueError(f"Error reading data files: {e}")

    n = len(strain)
    raw_data=np.empty((n, 13), dtype=GLOBAL_DTYPE)

    raw_data[:, 0:6] = strain[:, :]               # strain tensor (e11, e22, e33, e12, e23, e13, e21)
    raw_data[:, 6:12] = stress[:, :]              # stress tensor (s11, s22, s33, s12, s23, s13, s21)
    raw_data[:, 12] = state[:, 0]                   # state variable scalar (sv1)

    return raw_data

def compute_psi (stress, E=2e5, nu=0.3):
    '''Compute the free energy (psi) from the stress tensor.

    Parameters:
    - stress (numpy.ndarray): Numpy array containing the stress tensor with shape (n, 6).
    - E (float): Young's modulus.
    - nu (float): Poisson's ratio.

    Returns:
    - psi (numpy.ndarray): Numpy array containing the free energy with shape (n,).
    '''

    S = np.array([                              # Compliance matrix
        [1, -nu, -nu, 0, 0, 0],
        [-nu, 1, -nu, 0, 0, 0],
        [-nu, -nu, 1, 0, 0, 0],
        [0, 0, 0, 2*(1+nu), 0, 0],
        [0, 0, 0, 0, 2*(1+nu), 0],
        [0, 0, 0, 0, 0, 2*(1+nu)]
    ]) / E

    psi = 0.5 * np.einsum('ij,jk,ik->i', stress, S, stress)

    return psi

#%% ===================== 
# DEV
def validate_processed_eps_pl (raw_data):
    import matplotlib.pyplot as plt
    plt.rcParams["font.family"] = "serif"
    plt.rc('axes.formatter', use_mathtext=True)
    plt.rcParams["font.serif"] = "cmr10"
    plt.rcParams['font.size']=12
    pre_processed_data = pre_process_data(raw_data)
    eps_pl = pre_processed_data[:, 0:6]
    
    eq_eps_pl_processed = compute_eq_eps_pl(eps_pl)
    eq_eps_pl_raw = raw_data[raw_data[:, 12] > 1e-8, 12]

    plt.plot(eq_eps_pl_processed, label='Processed', marker='o', markersize=2, linestyle='--', linewidth=0.5, alpha=0.5)
    plt.plot(eq_eps_pl_raw, label='Raw', marker='o', markersize=2, linestyle='--', linewidth=0.5, alpha=0.5)
    plt.legend()
    plt.show()

def compute_eq_eps_pl(eps_pl):
    eps_pl = np.vstack((np.zeros(6), eps_pl)) # Adding a zero at the beginning as the very last elastic increment
    delta_eps_pl = eps_pl[1:] - eps_pl[:-1]
    # print(delta_eps_pl.shape)
    eq_pl_strain = np.zeros(len(eps_pl))
    acc = 0.0
    for i in range (0, len(eps_pl)-1):
        dev = deviatoric(delta_eps_pl[i])
        d_eq = np.sqrt(2.0 / 3.0 * np.dot(dev, dev))
        acc += d_eq
        eq_pl_strain[i] = acc

    return eq_pl_strain

def deviatoric(voigt):
    # Extract hydrostatic part and subtract it
    hydro = (voigt[0] + voigt[1] + voigt[2]) / 3.0
    dev = voigt.copy()
    dev[0] -= hydro
    dev[1] -= hydro
    dev[2] -= hydro
    return dev
#%% =====================
def pre_process_data(raw_data, threshold=1e-8):
    '''Pre-process the raw data to get the inputs and targets of the network.\n
    The inputs are the plastic strain and the targets are the stress and the maximum free energy.
    The maximum free energy is computed by taking the maximum of the free energy up to the current time step.
    The increments before the first plasticity step are discarded.

    Parameters:
    - raw_data (numpy.ndarray): Numpy array containing the raw data.
    - threshold (float): Threshold for detecting the first plasticity step.

    Returns:
    - pre_processed_data (numpy.ndarray): Numpy array containing the pre-processed data.
    '''
    E = 2e5
    nu = 0.3
    S = np.array([                              # Compliance matrix
        [1, -nu, -nu, 0, 0, 0],
        [-nu, 1, -nu, 0, 0, 0],
        [-nu, -nu, 1, 0, 0, 0],
        [0, 0, 0, 2*(1+nu), 0, 0],
        [0, 0, 0, 0, 2*(1+nu), 0],
        [0, 0, 0, 0, 0, 2*(1+nu)]
    ]) / E

    epsilon = raw_data[:, 0:6]
    sigma = raw_data[:, 6:12]
    acc_eq_pl_eps = raw_data[:, 12]
    first_plastc_idx = np.argmax(acc_eq_pl_eps > threshold)

    eps_el = np.matmul(sigma, S.T)                  # elastic strain = S * sigma
    eps_pl = epsilon - eps_el
    psi = compute_psi(sigma, E, nu)
    psi_max = np.maximum.accumulate(psi)

    sigma_valid = sigma[first_plastc_idx:]
    eps_pl_valid = eps_pl[first_plastc_idx:]
    psi_max_valid = psi_max[first_plastc_idx:]

    assert len(sigma_valid) == len(eps_pl_valid) == len(psi_max_valid)

    n = len(sigma_valid)
    pre_processed_data = np.empty((n, 13), dtype=GLOBAL_DTYPE)

    pre_processed_data[:, 0:6] = eps_pl_valid[:, 0:6]                 # plastic strain @ time=t (e_pl_11, e_pl_22, e_pl_33, e_pl_12, e_pl_23, e_pl_13)
    pre_processed_data[:, 6:12] = sigma_valid[:, 0:6]                 # stress @ time=t (s11, s22, s33, s12, s23, s13)
    pre_processed_data[:, 12] = psi_max_valid                         # maximum free energy @ time=t (psi)

    return pre_processed_data

def process_data(train_data, val_data, test_data=None, eps=1e-8):
    '''Normalize the datasets and extract the normalizing parameters.
    Parameters:
    - train_data (array-like): Training data.
    - val_data (array-like): Validation data.
    - test_data (array-like, optional): Test data.
    '''
    assert train_data.shape[1] == val_data.shape[1]
    num_train_samples = train_data.shape[0]
    TrVal_data = np.vstack((train_data, val_data))
    n_TrVal_data, norm_params = normalize_data(TrVal_data, eps=eps)
    n_train_data = n_TrVal_data[: num_train_samples, :]
    n_val_data = n_TrVal_data[num_train_samples:, :]
    if test_data is not None:
        assert train_data.shape[1] == test_data.shape[1]
        n_test_data = apply_normalization(data=test_data, normal_params=norm_params)
        return n_train_data, n_val_data, n_test_data, norm_params
    else:
        return n_train_data, n_val_data, norm_params

def normalize_data(data, eps=1e-8):
    '''Normalize the data using the Min-Max normalization and extract the normalizing parameters.'''
    norm_params = get_α_β(data, eps=eps)
    nrml_data = apply_normalization(data, norm_params)
    return nrml_data, norm_params

def get_α_β(data, norm=True, norm_01=False, no_norm=False, eps=1e-8):
    ''' Compute the normalization parameters (α & β).\n
        "no_norm" is for the case when normalizing/standardizing is not considered.\n
        "norm_01" is for the case when the scaling range is [0, 1].'''
    n_rows, n_cols = data.shape
    norm_params = list()

    for i in range(0, n_cols):
        u = data[:, i]
        if no_norm == False:
            if norm == True:
                if norm_01 == False:
                    u_max = np.max(u)
                    u_min = np.min(u)
                    ## DEV: For pure elastic datasets where Dz_min = Dz_max = 0
                    if np.abs(u_max - u_min) < eps:
                        α=1; β=u_min
                    else:
                        α = (u_max - u_min) / 2.; β = np.average((u_max, u_min))
                else: α = np.max(u); β = 0.
            elif norm == False:
                α = np.std(u, axis=0); β = np.mean(u, axis=0)
        else: α = 1.; β = 0.

        norm_params.append((GLOBAL_DTYPE(α), GLOBAL_DTYPE(β)))

    return np.array(norm_params, dtype=GLOBAL_DTYPE)

def apply_normalization(data, normal_params: list):
    '''Normalize the data using the given normalizing parameters.'''
    n_rows, n_cols = data.shape
    nrml_data = np.empty((n_rows, n_cols), dtype=GLOBAL_DTYPE)
    assert n_cols == len(normal_params)
    for i in range(0, n_cols):
        u = data[:, i]
        α = normal_params[i][0]; β = normal_params[i][1]
        nrml_data[:, i] = (u - β) / α

    return nrml_data