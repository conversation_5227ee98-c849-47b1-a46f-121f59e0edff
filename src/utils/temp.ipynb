from funcs import get_raw_data, pre_process_data, process_data
from plots import Plotter

plotter = Plotter()

train_data=pre_process_data(get_raw_data('12.5',2e5))
# val_data=pre_process_data(get_raw_data('10.2',2e5))

plotter.plot_data_scatter(train_data, axis_dict={'x':0, 'y':6, 'z':12})

print(train_data.shape)
print(train_data[3,:])
# print(train_data)

import numpy as np

def compute_eq_pl_strain(eps_pl):
    eps_pl = np.vstack((np.zeros(6), eps_pl))
    delta_eps_pl = eps_pl[1:] - eps_pl[:-1]
    print(delta_eps_pl.shape)
    eq_pl_strain = np.zeros(len(eps_pl))
    acc = 0.0
    # eq_pl_strain[0] = 0.0
    for i in range(1, len(eps_pl)):
        dev = deviatoric(delta_eps_pl[i-1])
        d_eq = np.sqrt(2.0 / 3.0 * np.dot(dev, dev))
        acc += d_eq
        eq_pl_strain[i-1] = acc
    return eq_pl_strain

def deviatoric(voigt):
    # Extract hydrostatic part and subtract it
    hydro = (voigt[0] + voigt[1] + voigt[2]) / 3.0
    dev = voigt.copy()
    dev[0] -= hydro
    dev[1] -= hydro
    dev[2] -= hydro
    return dev


train_data=pre_process_data(get_raw_data('12.3',2e5))

eps_pl_processed = train_data[:, 0:6]
eq_pl_pl_processed = compute_eq_pl_strain(eps_pl_processed)

eq_pl_eps_raw = get_raw_data('12.3',2e5)[:, 12]

print(eq_pl_eps_raw)
print(eq_pl_pl_processed)

# %%

print(train_data)

eps_pl_processed = train_data[:, 0:6]
eq_pl_eps_raw = get_raw_data('12.3',2e5)[:, 12]

print(eps_pl_processed)

print(eq_pl_eps_raw)

from funcs import validate_processed_eps_pl

validate_processed_eps_pl(get_raw_data('12.5',2e5))

