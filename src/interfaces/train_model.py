"""
This script serves as the training interface for the model.

Date: 2025-07-08
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import random
import sys

import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src/utils')

from funcs import get_raw_data, pre_process_data, process_data
from nn_model import NN_Model

#%% ---- Set Random Seed ----
seed = 42
random.seed(seed)
np.random.seed(seed)
tf.random.set_seed(seed)

#%% ---- Load Data ----
E=2e5
dataset_name = '10'
train_data=pre_process_data(get_raw_data(f'{dataset_name}.1',E))
val_data=pre_process_data(get_raw_data(f'{dataset_name}.2',E))

n_train_data, n_val_data, norm_params = process_data(train_data, val_data)